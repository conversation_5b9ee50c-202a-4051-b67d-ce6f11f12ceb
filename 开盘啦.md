# 开盘啦项目深度分析文档

## 项目概述

开盘啦是一款专业的股票数据分析应用，专注于为投资者提供多维市场资金分析。该软件运用大数据与人工智能技术，整合实时行情监测、资金流向分析、龙虎榜数据等功能，帮助投资者进行投资决策。

## 第一部分：功能模块分析

### 1. 六大独创优势功能

#### 1.1 精准股票分类
**功能描述：**
- 将股票按资金类型分类：庄股、游资票、基金票等
- 提供股票分类标识，让用户一眼识别股票类型
- 基于大数据研究的专业分类体系

**核心价值：**
- 帮助投资者快速识别不同资金属性的股票
- 提高选股效率，省时高效
- 为投资策略提供重要参考

#### 1.2 集合竞价异动
**功能描述：**
- 实时监控集合竞价阶段的资金挂单数据
- 结合题材机会进行人工智能分析
- 优选大概率上涨个股
- 提供竞价阶段的异动提醒

**核心价值：**
- 提前把握市场热点和资金流向
- 在开盘前发现投资机会
- 引领股票资金数据化时代

#### 1.3 区间统计/行情
**功能描述：**
- 行情区间统计可随意拉动查看
- 顺势找出逆势牛股
- 专业研究员精选概念组合
- 概念指数更贴合实际市场表现

**核心价值：**
- 灵活的时间区间分析
- 发现逆势强势股票
- 专业的概念板块分析

#### 1.4 商品现货价格
**功能描述：**
- 覆盖各行业商品涨价信息
- 打破行业信息不对称
- 提供精选股票池
- 商品个股联动分析

**核心价值：**
- 宏观商品价格与股票联动分析
- 行业轮动投资机会发现
- 信息不对称优势

#### 1.5 实时龙虎榜
**功能描述：**
- 实时监控大单主力资金流入流出
- 顶尖的计算方式
- 相当于盘中窥探龙虎榜
- 资金强弱实时掌握

**核心价值：**
- 实时资金流向监控
- 主力资金动向追踪
- 盘中决策支持

#### 1.6 深度龙虎榜分析
**功能描述：**
- 权威的深度龙虎榜数据分析
- 全面的主力研究和游资解密
- 挖掘市场资金合力
- 按概念分类解读上榜股票
- 关联个股协同操作营业部分析
- 知名游资标签：方新侠、赵老哥、上海超短、炒股养家等
- 35个官方组合一键订阅
- 自定义组合功能

**核心价值：**
- 深度主力资金分析
- 游资操作模式研究
- 个性化投资组合管理

### 2. 其他核心功能

#### 2.1 自选股宫格
- 手机宫格模式展示
- 多股K线并列展示
- 实时走势全面掌握

#### 2.2 大盘直播
- 文字直播形式
- 实时点评热门板块个股
- 解读股市走势
- 聚焦关键板块

#### 2.3 市场情绪
- 涨跌停个股统计
- 板块/权重表现评估
- 跟踪两市量能变化
- 北向资金动态监控
- 综合评定市场强度

#### 2.4 行情-打板
- 集合竞价阶段涨停委买额监控
- 提前把握资金对热门个股预期
- 板块资金预期分析

#### 2.5 复盘啦
- 当日盘面亮点梳理
- 亏钱效应提示
- 各板块核心个股整合
- 市场炒作高度梯队排列

#### 2.6 北向资金
- 特色时间轴功能
- 自主追踪各时段北向资金操作
- 便捷的资金流向监控

#### 2.7 明天炒什么
- 大数据总结热门股市资讯
- 预测明日热点板块
- 题材机会挖掘
- 投资机会提示

#### 2.8 题材库
- 小表格一图梳理题材炒作
- 题材热度排行
- 概念板块轮动分析
- 题材生命周期追踪

#### 2.9 狙击龙头
- 龙头股识别算法
- 板块龙头实时监控
- 龙头股轮动分析
- 龙头股买卖点提示

#### 2.10 涨停基因
- 涨停股票基因分析
- 涨停概率预测
- 涨停板复制分析
- 连板股票追踪

#### 2.11 选股工具
- 多维度选股条件
- 自定义选股策略
- 智能选股推荐
- 选股结果回测

#### 2.12 文章推荐
- 股市开盘早知道资讯
- 午间视频直播
- 涨停板复盘
- 一图复盘
- 龙虎榜中榜
- 北向资讯
- 股市教学文章

## 第二部分：数据结构与计算方法分析

### 1. 精准股票分类

#### 1.1 特征工程算法
```python
def extract_stock_classification_features(stock_code, days=60):
    """
    股票分类特征提取算法
    """
    # 获取历史数据
    kline_data = get_kline_data(stock_code, days)
    dragon_tiger_data = get_dragon_tiger_data(stock_code, days)
    fund_flow_data = get_fund_flow_data(stock_code, days)

    features = {}

    # 1. 技术指标特征
    features.update(extract_technical_features(kline_data))

    # 2. 资金流向特征
    features.update(extract_fund_flow_features(fund_flow_data))

    # 3. 交易行为特征
    features.update(extract_trading_behavior_features(kline_data))

    # 4. 龙虎榜特征
    features.update(extract_dragon_tiger_features(dragon_tiger_data))

    # 5. 市场表现特征
    features.update(extract_market_performance_features(kline_data))

    return features

def extract_technical_features(kline_data):
    """提取技术指标特征"""
    df = pd.DataFrame(kline_data)

    features = {}

    # 价格相关特征
    features['avg_turnover_rate'] = df['turnover_rate'].mean()
    features['std_turnover_rate'] = df['turnover_rate'].std()
    features['avg_amplitude'] = ((df['high_price'] - df['low_price']) / df['close_price']).mean()
    features['price_volatility'] = df['close_price'].pct_change().std()

    # 成交量特征
    features['avg_volume_ratio'] = df['volume'].mean() / df['volume'].rolling(20).mean().mean()
    features['volume_price_correlation'] = df['volume'].corr(df['close_price'])

    # 趋势特征
    features['price_trend'] = (df['close_price'].iloc[-1] - df['close_price'].iloc[0]) / df['close_price'].iloc[0]
    features['volume_trend'] = (df['volume'].iloc[-10:].mean() - df['volume'].iloc[:10].mean()) / df['volume'].iloc[:10].mean()

    return features

def extract_fund_flow_features(fund_flow_data):
    """提取资金流向特征"""
    if not fund_flow_data:
        return {}

    df = pd.DataFrame(fund_flow_data)

    features = {}

    # 大单资金特征
    features['large_inflow_ratio'] = (df['large_buy_amount'] - df['large_sell_amount']).sum() / df['large_buy_amount'].sum()
    features['large_fund_stability'] = 1 - (df['large_buy_amount'] - df['large_sell_amount']).std() / df['large_buy_amount'].mean()
    features['large_fund_frequency'] = (df['large_buy_amount'] > 0).sum() / len(df)

    # 主力资金特征
    features['main_force_avg_ratio'] = df['main_force_ratio'].mean()
    features['main_force_consistency'] = (df['main_force_ratio'] > 0.5).sum() / len(df)

    # 资金流向模式
    net_flows = df['large_buy_amount'] - df['large_sell_amount']
    features['fund_flow_pattern'] = calculate_fund_flow_pattern(net_flows)

    return features

def extract_trading_behavior_features(kline_data):
    """提取交易行为特征"""
    df = pd.DataFrame(kline_data)

    features = {}

    # 控盘度特征
    features['control_degree'] = calculate_control_degree(df)

    # 拉升模式特征
    features['pull_up_frequency'] = calculate_pull_up_frequency(df)
    features['end_of_day_pull_frequency'] = calculate_eod_pull_frequency(df)

    # 震荡特征
    features['consolidation_ratio'] = calculate_consolidation_ratio(df)

    # 突破特征
    features['breakout_frequency'] = calculate_breakout_frequency(df)

    return features

def calculate_control_degree(df):
    """计算控盘度"""
    # 基于换手率和价格稳定性计算控盘度
    avg_turnover = df['turnover_rate'].mean()
    price_stability = 1 - df['close_price'].pct_change().std()

    # 低换手率 + 高价格稳定性 = 高控盘度
    control_degree = (1 - min(avg_turnover / 10, 1)) * price_stability
    return control_degree
```

#### 1.2 机器学习分类模型
```python
import xgboost as xgb
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report

class StockClassificationModel:
    """
    股票分类机器学习模型
    """

    def __init__(self):
        self.models = {
            'xgboost': xgb.XGBClassifier(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.1,
                random_state=42
            ),
            'random_forest': RandomForestClassifier(
                n_estimators=200,
                max_depth=10,
                random_state=42
            )
        }
        self.feature_importance = {}
        self.label_encoder = {}

    def prepare_training_data(self, stock_list, days=60):
        """准备训练数据"""
        features_list = []
        labels_list = []

        for stock_code in stock_list:
            # 提取特征
            features = extract_stock_classification_features(stock_code, days)

            # 获取标签（人工标注或基于规则生成）
            label = self.get_stock_label(stock_code, features)

            if features and label:
                features_list.append(features)
                labels_list.append(label)

        # 转换为DataFrame
        features_df = pd.DataFrame(features_list)
        labels_series = pd.Series(labels_list)

        # 处理缺失值
        features_df = features_df.fillna(features_df.mean())

        return features_df, labels_series

    def train_models(self, features_df, labels_series):
        """训练分类模型"""
        # 分割训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            features_df, labels_series, test_size=0.2, random_state=42, stratify=labels_series
        )

        results = {}

        for model_name, model in self.models.items():
            print(f"Training {model_name}...")

            # 训练模型
            model.fit(X_train, y_train)

            # 预测
            y_pred = model.predict(X_test)

            # 评估
            report = classification_report(y_test, y_pred, output_dict=True)
            results[model_name] = {
                'model': model,
                'accuracy': report['accuracy'],
                'report': report
            }

            # 特征重要性
            if hasattr(model, 'feature_importances_'):
                self.feature_importance[model_name] = dict(
                    zip(features_df.columns, model.feature_importances_)
                )

            print(f"{model_name} Accuracy: {report['accuracy']:.4f}")

        return results

    def get_stock_label(self, stock_code, features):
        """
        基于规则和特征生成股票标签
        """
        # 庄股特征：低换手率、高控盘度、价格稳定
        if (features.get('avg_turnover_rate', 0) < 2 and
            features.get('control_degree', 0) > 0.7 and
            features.get('price_volatility', 1) < 0.03):
            return '庄股'

        # 游资票特征：高换手率、快进快出、高波动
        elif (features.get('avg_turnover_rate', 0) > 8 and
              features.get('price_volatility', 0) > 0.05 and
              features.get('large_fund_frequency', 0) > 0.3):
            return '游资票'

        # 基金票特征：中等换手率、稳定资金流入、机构持仓
        elif (2 <= features.get('avg_turnover_rate', 0) <= 8 and
              features.get('large_fund_stability', 0) > 0.6 and
              features.get('main_force_avg_ratio', 0) > 0.4):
            return '基金票'

        # 混合型
        else:
            return '混合型'

    def predict_stock_type(self, stock_code, use_ensemble=True):
        """预测股票类型"""
        # 提取特征
        features = extract_stock_classification_features(stock_code)
        features_df = pd.DataFrame([features])
        features_df = features_df.fillna(0)

        if use_ensemble:
            # 集成预测
            predictions = {}
            confidences = {}

            for model_name, model_info in self.models.items():
                model = model_info['model']
                pred = model.predict(features_df)[0]
                prob = model.predict_proba(features_df)[0]

                predictions[model_name] = pred
                confidences[model_name] = prob.max()

            # 加权投票
            weighted_pred = self.ensemble_predict(predictions, confidences)
            avg_confidence = sum(confidences.values()) / len(confidences)

            return {
                'prediction': weighted_pred,
                'confidence': avg_confidence,
                'individual_predictions': predictions,
                'individual_confidences': confidences
            }
        else:
            # 使用最佳模型
            best_model = max(self.models.items(), key=lambda x: x[1]['accuracy'])
            model = best_model[1]['model']

            pred = model.predict(features_df)[0]
            prob = model.predict_proba(features_df)[0]

            return {
                'prediction': pred,
                'confidence': prob.max(),
                'model_used': best_model[0]
            }

    def ensemble_predict(self, predictions, confidences):
        """集成预测"""
        # 基于置信度的加权投票
        vote_weights = {}

        for model_name, pred in predictions.items():
            weight = confidences[model_name]
            if pred not in vote_weights:
                vote_weights[pred] = 0
            vote_weights[pred] += weight

        # 返回权重最高的预测
        return max(vote_weights.items(), key=lambda x: x[1])[0]
```

### 2. 集合竞价异动

#### 2.1 集合竞价异动检测算法
```python
def detect_auction_anomaly(auction_data, historical_data, stock_code):
    """
    集合竞价异动检测核心算法
    """
    anomalies = []

    # 1. 价格异动检测
    price_anomaly = detect_price_anomaly(auction_data, historical_data)

    # 2. 成交量异动检测
    volume_anomaly = detect_volume_anomaly(auction_data, historical_data)

    # 3. 资金流向异动检测
    fund_flow_anomaly = detect_fund_flow_anomaly(auction_data)

    # 4. 委托撤单异动检测
    order_cancel_anomaly = detect_order_cancel_anomaly(auction_data)

    # 5. 大单集中度异动检测
    large_order_anomaly = detect_large_order_concentration(auction_data)

    # 综合异动评分
    total_score = (
        price_anomaly['score'] * 0.25 +
        volume_anomaly['score'] * 0.2 +
        fund_flow_anomaly['score'] * 0.25 +
        order_cancel_anomaly['score'] * 0.15 +
        large_order_anomaly['score'] * 0.15
    )

    return {
        'stock_code': stock_code,
        'total_score': total_score,
        'price_anomaly': price_anomaly,
        'volume_anomaly': volume_anomaly,
        'fund_flow_anomaly': fund_flow_anomaly,
        'order_cancel_anomaly': order_cancel_anomaly,
        'large_order_anomaly': large_order_anomaly,
        'is_anomaly': total_score > 0.7,  # 阈值可调
        'anomaly_level': get_anomaly_level(total_score)
    }

def detect_price_anomaly(auction_data, historical_data):
    """价格异动检测"""
    current_price = auction_data[-1].auction_price
    yesterday_close = historical_data[-1].close_price

    # 计算涨跌幅
    change_percent = (current_price - yesterday_close) / yesterday_close * 100

    # 计算历史同期价格波动标准差
    historical_changes = []
    for i in range(min(20, len(historical_data)-1)):
        hist_change = (historical_data[-(i+1)].open_price - historical_data[-(i+2)].close_price) / historical_data[-(i+2)].close_price * 100
        historical_changes.append(hist_change)

    if len(historical_changes) > 0:
        avg_change = sum(historical_changes) / len(historical_changes)
        std_change = (sum((x - avg_change) ** 2 for x in historical_changes) / len(historical_changes)) ** 0.5

        # Z-score计算异常程度
        z_score = abs(change_percent - avg_change) / (std_change + 0.01)
        anomaly_score = min(z_score / 3, 1.0)  # 标准化到0-1
    else:
        anomaly_score = 0

    return {
        'change_percent': change_percent,
        'z_score': z_score if 'z_score' in locals() else 0,
        'score': anomaly_score,
        'description': f"竞价涨跌幅{change_percent:.2f}%，异常程度{anomaly_score:.2f}"
    }

def detect_volume_anomaly(auction_data, historical_data):
    """成交量异动检测"""
    current_volume = sum(data.auction_volume for data in auction_data)

    # 计算历史同期成交量均值
    historical_volumes = [data.volume for data in historical_data[-20:]]
    avg_volume = sum(historical_volumes) / len(historical_volumes) if historical_volumes else 1

    # 成交量比率
    volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0

    # 异常评分
    if volume_ratio > 5:
        anomaly_score = 1.0
    elif volume_ratio > 3:
        anomaly_score = 0.8
    elif volume_ratio > 2:
        anomaly_score = 0.6
    elif volume_ratio > 1.5:
        anomaly_score = 0.4
    else:
        anomaly_score = 0.2

    return {
        'current_volume': current_volume,
        'avg_volume': avg_volume,
        'volume_ratio': volume_ratio,
        'score': anomaly_score,
        'description': f"竞价量比{volume_ratio:.2f}，异常程度{anomaly_score:.2f}"
    }

def detect_fund_flow_anomaly(auction_data):
    """资金流向异动检测"""
    total_buy_amount = 0
    total_sell_amount = 0
    large_buy_amount = 0
    large_sell_amount = 0

    for data in auction_data:
        buy_amount = data.bid_price * data.bid_volume
        sell_amount = data.ask_price * data.ask_volume

        total_buy_amount += buy_amount
        total_sell_amount += sell_amount

        # 大单定义：单笔超过50万
        if buy_amount > 500000:
            large_buy_amount += buy_amount
        if sell_amount > 500000:
            large_sell_amount += sell_amount

    # 资金净流入
    net_inflow = total_buy_amount - total_sell_amount
    large_net_inflow = large_buy_amount - large_sell_amount

    # 大单占比
    total_amount = total_buy_amount + total_sell_amount
    large_ratio = (large_buy_amount + large_sell_amount) / total_amount if total_amount > 0 else 0

    # 异常评分
    net_inflow_score = min(abs(net_inflow) / 10000000, 1.0)  # 千万为满分
    large_ratio_score = large_ratio  # 大单占比直接作为评分

    anomaly_score = (net_inflow_score + large_ratio_score) / 2

    return {
        'net_inflow': net_inflow,
        'large_net_inflow': large_net_inflow,
        'large_ratio': large_ratio,
        'score': anomaly_score,
        'description': f"资金净流入{net_inflow/10000:.0f}万，大单占比{large_ratio:.2%}"
    }
```

#### 2.2 题材关联分析算法
```python
def analyze_concept_correlation(stock_code, auction_anomaly_data, news_data, concept_data):
    """
    题材关联分析算法
    """
    correlations = []

    # 1. 基于新闻关联
    news_correlations = analyze_news_correlation(stock_code, news_data)

    # 2. 基于概念板块关联
    concept_correlations = analyze_concept_correlation_internal(stock_code, concept_data)

    # 3. 基于同板块股票异动关联
    sector_correlations = analyze_sector_correlation(stock_code, auction_anomaly_data)

    # 4. 计算题材热度
    concept_heat = calculate_concept_heat(concept_correlations, auction_anomaly_data)

    return {
        'news_correlations': news_correlations,
        'concept_correlations': concept_correlations,
        'sector_correlations': sector_correlations,
        'concept_heat': concept_heat,
        'main_concepts': get_main_concepts(concept_correlations, concept_heat)
    }

def calculate_concept_heat(concept_correlations, anomaly_data):
    """计算概念热度"""
    concept_heat = {}

    for concept in concept_correlations:
        concept_code = concept['concept_code']
        concept_stocks = concept['related_stocks']

        # 统计概念内异动股票数量
        anomaly_count = 0
        total_anomaly_score = 0

        for stock_code in concept_stocks:
            if stock_code in anomaly_data:
                if anomaly_data[stock_code]['is_anomaly']:
                    anomaly_count += 1
                    total_anomaly_score += anomaly_data[stock_code]['total_score']

        # 计算热度评分
        heat_score = (anomaly_count / len(concept_stocks)) * (total_anomaly_score / anomaly_count if anomaly_count > 0 else 0)

        concept_heat[concept_code] = {
            'heat_score': heat_score,
            'anomaly_count': anomaly_count,
            'total_stocks': len(concept_stocks),
            'avg_anomaly_score': total_anomaly_score / anomaly_count if anomaly_count > 0 else 0
        }

    return concept_heat
```

### 3. 实时龙虎榜

#### 计算方法：

#### 3.1 主动买卖识别算法
```python
def identify_active_trade(trade_price, bid_price, ask_price, trade_volume):
    """
    主动买卖识别算法
    基于成交价格与买卖档位的关系判断主动性
    """
    mid_price = (bid_price + ask_price) / 2

    if trade_price >= ask_price:
        return "主动买入", trade_volume * trade_price
    elif trade_price <= bid_price:
        return "主动卖出", trade_volume * trade_price
    elif trade_price > mid_price:
        # 更接近卖价，倾向于主动买入
        confidence = (trade_price - mid_price) / (ask_price - mid_price)
        return "主动买入", trade_volume * trade_price * confidence
    else:
        # 更接近买价，倾向于主动卖出
        confidence = (mid_price - trade_price) / (mid_price - bid_price)
        return "主动卖出", trade_volume * trade_price * confidence
```

#### 3.2 大单资金流计算算法
```python
def calculate_fund_flow(trades_data, time_window=60):
    """
    实时资金流计算算法
    time_window: 时间窗口（秒）
    """
    current_time = datetime.now()
    window_start = current_time - timedelta(seconds=time_window)

    # 筛选时间窗口内的交易数据
    window_trades = [t for t in trades_data if t.timestamp >= window_start]

    large_buy_amount = 0    # 大单主动买入
    large_sell_amount = 0   # 大单主动卖出
    medium_buy_amount = 0   # 中单主动买入
    medium_sell_amount = 0  # 中单主动卖出
    small_buy_amount = 0    # 小单主动买入
    small_sell_amount = 0   # 小单主动卖出

    for trade in window_trades:
        trade_amount = trade.price * trade.volume
        trade_type, active_amount = identify_active_trade(
            trade.price, trade.bid_price, trade.ask_price, trade.volume
        )

        # 资金分类标准
        if trade_amount >= 500000:  # 50万以上为大单
            if trade_type == "主动买入":
                large_buy_amount += active_amount
            else:
                large_sell_amount += active_amount
        elif trade_amount >= 50000:  # 5万-50万为中单
            if trade_type == "主动买入":
                medium_buy_amount += active_amount
            else:
                medium_sell_amount += active_amount
        else:  # 5万以下为小单
            if trade_type == "主动买入":
                small_buy_amount += active_amount
            else:
                small_sell_amount += active_amount

    # 计算净流入
    large_net_inflow = large_buy_amount - large_sell_amount
    medium_net_inflow = medium_buy_amount - medium_sell_amount
    small_net_inflow = small_buy_amount - small_sell_amount
    total_net_inflow = large_net_inflow + medium_net_inflow + small_net_inflow

    return {
        'large_net_inflow': large_net_inflow,
        'medium_net_inflow': medium_net_inflow,
        'small_net_inflow': small_net_inflow,
        'total_net_inflow': total_net_inflow,
        'large_buy_amount': large_buy_amount,
        'large_sell_amount': large_sell_amount,
        'main_force_ratio': large_net_inflow / (large_buy_amount + large_sell_amount) if (large_buy_amount + large_sell_amount) > 0 else 0
    }
```

#### 3.3 实时龙虎榜排名算法
```python
def calculate_realtime_dragon_tiger_rank(all_stocks_data):
    """
    实时龙虎榜排名计算
    """
    rankings = []

    for stock_code, trades in all_stocks_data.items():
        fund_flow = calculate_fund_flow(trades)
        stock_info = get_stock_basic_info(stock_code)

        # 计算排名指标
        net_inflow_ratio = fund_flow['total_net_inflow'] / stock_info['market_cap'] * 100
        turnover_rate = calculate_turnover_rate(trades, stock_info['total_shares'])

        # 综合评分算法
        score = (
            fund_flow['large_net_inflow'] * 0.5 +  # 大单净流入权重50%
            net_inflow_ratio * stock_info['market_cap'] * 0.3 +  # 净流入比例权重30%
            turnover_rate * stock_info['market_cap'] * 0.2  # 换手率权重20%
        )

        rankings.append({
            'stock_code': stock_code,
            'net_inflow': fund_flow['total_net_inflow'],
            'large_net_inflow': fund_flow['large_net_inflow'],
            'main_force_ratio': fund_flow['main_force_ratio'],
            'turnover_rate': turnover_rate,
            'score': score
        })

    # 按综合评分排序
    rankings.sort(key=lambda x: x['score'], reverse=True)

    # 添加排名
    for i, item in enumerate(rankings):
        item['rank'] = i + 1

    return rankings
```

#### 3.4 主力资金识别算法
```python
def identify_main_force_behavior(trades_data, stock_code):
    """
    主力资金行为识别算法
    """
    behaviors = []

    # 1. 连续大单检测
    consecutive_large_trades = detect_consecutive_large_trades(trades_data)

    # 2. 对倒交易检测
    wash_trades = detect_wash_trading(trades_data)

    # 3. 尾盘拉升检测
    end_of_day_manipulation = detect_eod_manipulation(trades_data)

    # 4. 集中成交检测
    concentrated_trading = detect_concentrated_trading(trades_data)

    return {
        'consecutive_large_trades': consecutive_large_trades,
        'wash_trades': wash_trades,
        'eod_manipulation': end_of_day_manipulation,
        'concentrated_trading': concentrated_trading,
        'main_force_score': calculate_main_force_score(
            consecutive_large_trades, wash_trades,
            end_of_day_manipulation, concentrated_trading
        )
    }

def detect_consecutive_large_trades(trades_data, min_amount=500000, min_count=5):
    """检测连续大单"""
    consecutive_count = 0
    max_consecutive = 0
    total_amount = 0

    for trade in trades_data:
        if trade.amount >= min_amount:
            consecutive_count += 1
            total_amount += trade.amount
        else:
            max_consecutive = max(max_consecutive, consecutive_count)
            consecutive_count = 0

    return {
        'max_consecutive': max_consecutive,
        'is_detected': max_consecutive >= min_count,
        'total_amount': total_amount
    }
```

### 4. 市场情绪分析

#### 4.1 市场情绪指标计算算法
```python
def calculate_market_sentiment(market_data):
    """
    市场情绪综合计算算法
    """
    total_stocks = len(market_data)
    limit_up_count = sum(1 for stock in market_data if stock.change_percent >= 9.9)
    limit_down_count = sum(1 for stock in market_data if stock.change_percent <= -9.9)
    advance_count = sum(1 for stock in market_data if stock.change_percent > 0)
    decline_count = sum(1 for stock in market_data if stock.change_percent < 0)

    # 基础情绪指标
    sentiment_index = (limit_up_count - limit_down_count) / total_stocks * 100
    money_effect = advance_count / total_stocks * 100
    panic_index = limit_down_count / (limit_up_count + limit_down_count + 1) * 100
    advance_decline_ratio = advance_count / (decline_count + 1)

    # 成交量情绪
    total_volume = sum(stock.volume for stock in market_data)
    avg_volume = total_volume / total_stocks
    volume_sentiment = calculate_volume_sentiment(market_data, avg_volume)

    # 综合情绪评分 (0-100)
    composite_sentiment = (
        sentiment_index * 0.3 +
        money_effect * 0.25 +
        (100 - panic_index) * 0.2 +
        min(advance_decline_ratio * 20, 100) * 0.15 +
        volume_sentiment * 0.1
    )

    return {
        'sentiment_index': sentiment_index,
        'money_effect': money_effect,
        'panic_index': panic_index,
        'advance_decline_ratio': advance_decline_ratio,
        'volume_sentiment': volume_sentiment,
        'composite_sentiment': composite_sentiment,
        'market_status': get_market_status(composite_sentiment)
    }

def get_market_status(sentiment_score):
    """根据情绪评分判断市场状态"""
    if sentiment_score >= 80:
        return "极度乐观"
    elif sentiment_score >= 60:
        return "乐观"
    elif sentiment_score >= 40:
        return "中性"
    elif sentiment_score >= 20:
        return "悲观"
    else:
        return "极度悲观"
```

#### 4.2 板块强度计算算法
```python
def calculate_sector_strength(sector_stocks, market_data):
    """
    板块强度综合计算算法
    """
    if not sector_stocks:
        return None

    # 1. 价格表现计算
    total_market_cap = sum(stock.market_cap for stock in sector_stocks)
    weighted_change = sum(
        stock.change_percent * (stock.market_cap / total_market_cap)
        for stock in sector_stocks
    )

    # 2. 资金流向计算
    total_net_inflow = sum(stock.net_inflow for stock in sector_stocks)
    avg_net_inflow = total_net_inflow / len(sector_stocks)

    # 3. 活跃度计算
    active_stocks_count = sum(
        1 for stock in sector_stocks
        if stock.turnover_rate > 2.0  # 换手率大于2%认为活跃
    )
    activity_ratio = active_stocks_count / len(sector_stocks)

    # 4. 龙头股表现
    leading_stock = max(sector_stocks, key=lambda x: x.market_cap)
    leading_performance = leading_stock.change_percent

    # 5. 涨停股数量
    limit_up_count = sum(
        1 for stock in sector_stocks
        if stock.change_percent >= 9.9
    )
    limit_up_ratio = limit_up_count / len(sector_stocks)

    # 6. 相对强度计算（相对于大盘）
    market_avg_change = sum(stock.change_percent for stock in market_data) / len(market_data)
    relative_strength = weighted_change - market_avg_change

    # 7. 综合强度评分计算
    strength_score = (
        weighted_change * 0.25 +           # 加权涨幅 25%
        (total_net_inflow / 1000000) * 0.2 +  # 资金净流入 20%
        activity_ratio * 20 * 0.15 +       # 活跃度 15%
        leading_performance * 0.15 +       # 龙头表现 15%
        limit_up_ratio * 50 * 0.15 +       # 涨停比例 15%
        relative_strength * 0.1            # 相对强度 10%
    )

    return {
        'weighted_change': weighted_change,
        'total_net_inflow': total_net_inflow,
        'avg_net_inflow': avg_net_inflow,
        'activity_ratio': activity_ratio,
        'leading_stock_code': leading_stock.stock_code,
        'leading_performance': leading_performance,
        'limit_up_count': limit_up_count,
        'limit_up_ratio': limit_up_ratio,
        'relative_strength': relative_strength,
        'strength_score': strength_score,
        'strength_level': get_strength_level(strength_score)
    }

def get_strength_level(score):
    """根据强度评分判断强度等级"""
    if score >= 8:
        return "极强"
    elif score >= 5:
        return "强"
    elif score >= 2:
        return "中等"
    elif score >= -2:
        return "弱"
    else:
        return "极弱"
```

#### 4.3 板块轮动分析算法
```python
def analyze_sector_rotation(historical_sector_data, days=20):
    """
    板块轮动分析算法
    """
    rotation_analysis = {}

    for sector_code, sector_data in historical_sector_data.items():
        if len(sector_data) < days:
            continue

        recent_data = sector_data[-days:]

        # 计算趋势强度
        trend_strength = calculate_trend_strength(recent_data)

        # 计算动量
        momentum = calculate_momentum(recent_data)

        # 计算资金流向趋势
        fund_flow_trend = calculate_fund_flow_trend(recent_data)

        # 预测轮动概率
        rotation_probability = predict_rotation_probability(
            trend_strength, momentum, fund_flow_trend
        )

        rotation_analysis[sector_code] = {
            'trend_strength': trend_strength,
            'momentum': momentum,
            'fund_flow_trend': fund_flow_trend,
            'rotation_probability': rotation_probability,
            'rotation_direction': get_rotation_direction(rotation_probability)
        }

    return rotation_analysis

def calculate_trend_strength(data):
    """计算趋势强度"""
    if len(data) < 5:
        return 0

    # 使用线性回归计算趋势
    x = list(range(len(data)))
    y = [d.strength_score for d in data]

    # 简单线性回归
    n = len(x)
    sum_x = sum(x)
    sum_y = sum(y)
    sum_xy = sum(x[i] * y[i] for i in range(n))
    sum_x2 = sum(x[i] ** 2 for i in range(n))

    slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x ** 2)

    return slope
```

### 6. 高效计算和实时处理方案

#### 6.1 实时计算架构
```python
class RealTimeCalculationEngine:
    """
    实时计算引擎
    基于事件驱动和流式处理
    """

    def __init__(self):
        self.redis_client = redis.Redis()
        self.kafka_consumer = KafkaConsumer('market-data')
        self.calculation_cache = {}
        self.sliding_windows = {}

    def process_market_data_stream(self):
        """处理实时市场数据流"""
        for message in self.kafka_consumer:
            trade_data = json.loads(message.value)

            # 并行处理多个计算任务
            with ThreadPoolExecutor(max_workers=4) as executor:
                futures = [
                    executor.submit(self.update_fund_flow, trade_data),
                    executor.submit(self.update_dragon_tiger_rank, trade_data),
                    executor.submit(self.update_sector_strength, trade_data),
                    executor.submit(self.update_market_sentiment, trade_data)
                ]

                # 等待所有计算完成
                for future in futures:
                    future.result()

    def update_fund_flow(self, trade_data):
        """更新资金流向（增量计算）"""
        stock_code = trade_data['stock_code']

        # 使用滑动窗口进行增量计算
        window_key = f"fund_flow:{stock_code}"

        if window_key not in self.sliding_windows:
            self.sliding_windows[window_key] = SlidingWindow(size=300)  # 5分钟窗口

        window = self.sliding_windows[window_key]

        # 添加新数据点
        window.add(trade_data)

        # 增量计算资金流向
        fund_flow = self.calculate_incremental_fund_flow(window)

        # 更新缓存
        self.redis_client.setex(
            f"realtime:fund_flow:{stock_code}",
            60,  # 1分钟过期
            json.dumps(fund_flow)
        )

class SlidingWindow:
    """滑动窗口数据结构"""

    def __init__(self, size=300):
        self.size = size
        self.data = deque(maxlen=size)
        self.sum_cache = {}

    def add(self, item):
        """添加数据项"""
        # 如果窗口满了，需要减去要被移除的项
        if len(self.data) == self.size:
            old_item = self.data[0]
            self.update_cache_remove(old_item)

        self.data.append(item)
        self.update_cache_add(item)

    def update_cache_add(self, item):
        """增量更新缓存（添加项）"""
        if 'total_amount' not in self.sum_cache:
            self.sum_cache['total_amount'] = 0
        self.sum_cache['total_amount'] += item.get('amount', 0)

    def update_cache_remove(self, item):
        """增量更新缓存（移除项）"""
        self.sum_cache['total_amount'] -= item.get('amount', 0)
```

#### 6.2 内存优化和缓存策略
```python
class OptimizedDataManager:
    """
    优化的数据管理器
    """

    def __init__(self):
        self.l1_cache = {}  # 内存缓存
        self.l2_cache = redis.Redis()  # Redis缓存
        self.cache_stats = {'hits': 0, 'misses': 0}

    def get_stock_data(self, stock_code, data_type):
        """多级缓存数据获取"""
        cache_key = f"{stock_code}:{data_type}"

        # L1缓存查找
        if cache_key in self.l1_cache:
            self.cache_stats['hits'] += 1
            return self.l1_cache[cache_key]

        # L2缓存查找
        l2_data = self.l2_cache.get(cache_key)
        if l2_data:
            data = json.loads(l2_data)
            # 回填L1缓存
            self.l1_cache[cache_key] = data
            self.cache_stats['hits'] += 1
            return data

        # 缓存未命中，从数据库加载
        self.cache_stats['misses'] += 1
        data = self.load_from_database(stock_code, data_type)

        # 更新缓存
        self.l1_cache[cache_key] = data
        self.l2_cache.setex(cache_key, 300, json.dumps(data))

        return data

    def batch_update_cache(self, updates):
        """批量更新缓存"""
        pipe = self.l2_cache.pipeline()

        for cache_key, data in updates.items():
            # 更新L1缓存
            self.l1_cache[cache_key] = data
            # 批量更新L2缓存
            pipe.setex(cache_key, 300, json.dumps(data))

        pipe.execute()
```

#### 6.3 并行计算优化
```python
import asyncio
import aioredis
from concurrent.futures import ProcessPoolExecutor

class ParallelCalculationEngine:
    """
    并行计算引擎
    """

    def __init__(self):
        self.process_pool = ProcessPoolExecutor(max_workers=8)
        self.redis_pool = aioredis.ConnectionPool.from_url("redis://localhost")

    async def calculate_all_stocks_parallel(self, stock_list):
        """并行计算所有股票指标"""

        # 将股票列表分批
        batch_size = 100
        batches = [stock_list[i:i+batch_size] for i in range(0, len(stock_list), batch_size)]

        # 并行处理每个批次
        tasks = []
        for batch in batches:
            task = asyncio.create_task(self.process_stock_batch(batch))
            tasks.append(task)

        # 等待所有批次完成
        results = await asyncio.gather(*tasks)

        # 合并结果
        all_results = {}
        for batch_result in results:
            all_results.update(batch_result)

        return all_results

    async def process_stock_batch(self, stock_batch):
        """处理股票批次"""
        loop = asyncio.get_event_loop()

        # 使用进程池进行CPU密集型计算
        future = loop.run_in_executor(
            self.process_pool,
            self.calculate_batch_indicators,
            stock_batch
        )

        return await future

    def calculate_batch_indicators(self, stock_batch):
        """批量计算指标（在子进程中执行）"""
        results = {}

        for stock_code in stock_batch:
            # 获取股票数据
            stock_data = self.get_stock_data_fast(stock_code)

            # 计算各种指标
            fund_flow = calculate_fund_flow_optimized(stock_data)
            technical_indicators = calculate_technical_indicators_fast(stock_data)

            results[stock_code] = {
                'fund_flow': fund_flow,
                'technical_indicators': technical_indicators
            }

        return results
```

#### 6.4 数据库查询优化
```python
class OptimizedDatabaseManager:
    """
    优化的数据库管理器
    """

    def __init__(self):
        self.connection_pool = create_connection_pool()
        self.query_cache = {}

    def get_realtime_data_batch(self, stock_codes, time_range):
        """批量获取实时数据"""

        # 构建批量查询SQL
        placeholders = ','.join(['%s'] * len(stock_codes))
        sql = f"""
        SELECT stock_code, trade_time, price, volume, amount,
               bid_price_1, ask_price_1, bid_volume_1, ask_volume_1
        FROM realtime_quotes
        WHERE stock_code IN ({placeholders})
        AND trade_time >= %s
        ORDER BY stock_code, trade_time
        """

        # 执行查询
        with self.connection_pool.get_connection() as conn:
            cursor = conn.cursor(dictionary=True)
            cursor.execute(sql, stock_codes + [time_range])
            results = cursor.fetchall()

        # 按股票代码分组
        grouped_results = {}
        for row in results:
            stock_code = row['stock_code']
            if stock_code not in grouped_results:
                grouped_results[stock_code] = []
            grouped_results[stock_code].append(row)

        return grouped_results

    def bulk_insert_fund_flow(self, fund_flow_data):
        """批量插入资金流向数据"""

        sql = """
        INSERT INTO realtime_fund_flow
        (stock_code, trade_date, time_stamp, large_buy_amount,
         large_sell_amount, net_flow, main_force_ratio)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        large_buy_amount = VALUES(large_buy_amount),
        large_sell_amount = VALUES(large_sell_amount),
        net_flow = VALUES(net_flow),
        main_force_ratio = VALUES(main_force_ratio)
        """

        # 准备批量数据
        batch_data = []
        for stock_code, data in fund_flow_data.items():
            batch_data.append((
                stock_code,
                data['trade_date'],
                data['time_stamp'],
                data['large_buy_amount'],
                data['large_sell_amount'],
                data['net_flow'],
                data['main_force_ratio']
            ))

        # 批量执行
        with self.connection_pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.executemany(sql, batch_data)
            conn.commit()
```

#### 6.5 实时推送优化
```python
class OptimizedWebSocketManager:
    """
    优化的WebSocket推送管理器
    """

    def __init__(self):
        self.connections = {}  # 按订阅类型分组连接
        self.subscription_map = {}  # 用户订阅映射
        self.push_queue = asyncio.Queue(maxsize=10000)
        self.batch_size = 100
        self.batch_timeout = 0.1  # 100ms批量推送

    async def start_push_worker(self):
        """启动推送工作器"""
        while True:
            batch = []
            start_time = time.time()

            # 收集批量数据
            while len(batch) < self.batch_size and (time.time() - start_time) < self.batch_timeout:
                try:
                    item = await asyncio.wait_for(self.push_queue.get(), timeout=0.01)
                    batch.append(item)
                except asyncio.TimeoutError:
                    break

            if batch:
                await self.push_batch(batch)

    async def push_batch(self, batch):
        """批量推送数据"""
        # 按订阅类型分组
        grouped_data = {}
        for item in batch:
            subscription_type = item['type']
            if subscription_type not in grouped_data:
                grouped_data[subscription_type] = []
            grouped_data[subscription_type].append(item['data'])

        # 并行推送到不同订阅组
        tasks = []
        for subscription_type, data_list in grouped_data.items():
            if subscription_type in self.connections:
                task = asyncio.create_task(
                    self.push_to_subscription_group(subscription_type, data_list)
                )
                tasks.append(task)

        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

    async def push_to_subscription_group(self, subscription_type, data_list):
        """推送到订阅组"""
        if subscription_type not in self.connections:
            return

        message = json.dumps({
            'type': subscription_type,
            'data': data_list,
            'timestamp': time.time()
        })

        # 并行推送到所有连接
        connections = list(self.connections[subscription_type])
        tasks = []

        for websocket in connections:
            task = asyncio.create_task(self.safe_send(websocket, message))
            tasks.append(task)

        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

    async def safe_send(self, websocket, message):
        """安全发送消息"""
        try:
            await websocket.send(message)
        except Exception as e:
            # 连接断开，从连接池中移除
            self.remove_connection(websocket)
```
```

### 5. 北向资金分析

#### 计算方法：
1. **资金流向计算**
   - 净流入 = 买入金额 - 卖出金额
   - 流入强度 = 净流入 / 当日额度
   - 持仓变化 = 当日持仓 - 前日持仓

2. **时间轴分析**
   - 分时段统计：开盘、上午、下午、尾盘
   - 计算各时段的资金流向特征
   - 识别异常流入流出时点

## 第三部分：数据源需求分析

### 1. 基础行情数据源

#### 1.1 实时行情数据
**数据需求：**
- 股票实时价格、成交量、成交额
- 分时数据（1分钟、5分钟级别）
- 买卖五档委托数据
- 逐笔成交数据

**推荐数据源：**
- **Tushare Pro**：提供实时行情API
- **新浪财经API**：免费实时行情接口
- **腾讯财经API**：实时股价数据
- **Wind API**：专业金融数据（付费）
- **同花顺iFinD**：专业数据终端（付费）

#### 1.2 历史行情数据
**数据需求：**
- 日K线、周K线、月K线数据
- 复权价格数据
- 技术指标数据

**推荐数据源：**
- **Tushare**：免费历史数据
- **AKShare**：开源金融数据库
- **JoinQuant**：聚宽数据平台
- **RiceQuant**：米筐数据平台

### 2. 集合竞价数据源

#### 2.1 集合竞价实时数据
**数据需求：**
- 9:15-9:25集合竞价期间的委托数据
- 实时竞价价格和数量
- 委托撤单数据

**推荐数据源：**
- **Level-2数据**：交易所官方数据（付费）
- **同花顺Level-2**：专业行情数据
- **东方财富Choice**：金融数据终端
- **通达信Level-2**：实时行情数据

### 3. 龙虎榜数据源

#### 3.1 官方龙虎榜数据
**数据需求：**
- 每日龙虎榜公布数据
- 营业部席位信息
- 买卖金额明细

**推荐数据源：**
- **交易所官网**：上交所、深交所龙虎榜
- **Tushare Pro**：龙虎榜数据接口
- **东方财富网**：龙虎榜数据爬取
- **同花顺**：龙虎榜数据API

### 4. 基本面数据源

#### 4.1 公司基本信息
**数据需求：**
- 股票基本信息（代码、名称、行业等）
- 财务报表数据
- 股本结构数据

**推荐数据源：**
- **Tushare Pro**：基本面数据接口
- **Wind数据库**：专业财务数据
- **Choice数据**：东方财富数据终端
- **巨潮资讯网**：官方公告数据

### 5. 资金流向数据源

#### 5.1 北向资金数据
**数据需求：**
- 沪深港通资金流向
- 个股北向资金持仓
- 实时北向资金流入流出

**推荐数据源：**
- **港交所官网**：沪深港通数据
- **Tushare Pro**：北向资金接口
- **东方财富**：北向资金数据
- **同花顺**：港资流向数据

### 6. 新闻和公告数据源

#### 6.1 新闻资讯数据
**数据需求：**
- 财经新闻数据
- 个股相关新闻
- 行业热点新闻

**推荐数据源：**
- **财联社API**：实时财经新闻
- **新浪财经**：新闻数据爬取
- **东方财富资讯**：股票新闻
- **金融界**：财经资讯数据

### 7. 宏观经济数据源

#### 7.1 宏观经济指标
**数据需求：**
- GDP、CPI、PPI等宏观指标
- 货币政策数据
- 利率汇率数据

**推荐数据源：**
- **国家统计局**：官方宏观数据
- **中国人民银行**：货币政策数据
- **Tushare Pro**：宏观数据接口
- **Wind宏观数据库**：专业宏观数据

#### 7.2 商品期货数据
**数据需求：**
- 大宗商品价格
- 期货合约数据
- 现货价格数据

**推荐数据源：**
- **上海期货交易所**：期货数据
- **大连商品交易所**：商品期货
- **郑州商品交易所**：农产品期货
- **生意社**：现货价格数据

### 8. 数据获取策略

#### 8.1 免费数据源组合
- **Tushare** + **AKShare** + **新浪财经API**
- 适合个人开发者和小型项目
- 数据完整度：70-80%

#### 8.2 付费数据源组合
- **Wind** + **Choice** + **Level-2数据**
- 适合专业机构和商业项目
- 数据完整度：95%+

#### 8.3 混合数据源策略
- 核心数据使用付费源保证质量
- 辅助数据使用免费源降低成本
- 数据完整度：85-90%

## 第四部分：数据库设计方案

### 1. 数据库架构设计

#### 1.1 分库分表策略
```
主数据库 (MySQL/PostgreSQL)
├── 基础数据库 (basic_data)
├── 行情数据库 (market_data)
├── 资金数据库 (fund_data)
├── 分析数据库 (analysis_data)
└── 用户数据库 (user_data)

时序数据库 (InfluxDB/TimescaleDB)
├── 实时行情时序数据
├── 分钟级K线数据
├── 资金流向时序数据
└── 集合竞价时序数据

缓存数据库 (Redis)
├── 实时行情缓存
├── 热点数据缓存
├── 用户会话缓存
└── 计算结果缓存
```

### 2. 核心数据表设计

#### 2.1 基础数据表
- **stock_basic**: 股票基本信息表
  - stock_code, stock_name, industry, sector, list_date, market, status
- **concept_basic**: 概念板块基本信息表
  - concept_code, concept_name, concept_type, description
- **concept_stocks**: 概念成分股表
  - concept_code, stock_code, weight, in_date, out_date, status
- **trading_calendar**: 交易日历表
  - trade_date, is_open, exchange

#### 2.2 行情数据表
- **daily_kline_YYYY**: 日K线数据表（按年分表）
  - stock_code, trade_date, open_price, high_price, low_price, close_price, volume, amount, turnover_rate
- **realtime_quotes**: 实时行情表
  - stock_code, current_price, change_amount, change_percent, volume, amount, bid_price_1, ask_price_1
- **minute_kline**: 分钟K线数据表
  - stock_code, trade_time, open_price, high_price, low_price, close_price, volume, amount

#### 2.3 集合竞价数据表
- **auction_data**: 集合竞价数据表
  - stock_code, trade_date, auction_time, auction_price, auction_volume, bid_price, bid_volume, ask_price, ask_volume
- **auction_anomaly**: 集合竞价异动表
  - stock_code, trade_date, anomaly_type, anomaly_score, price_change_percent, volume_ratio, fund_flow, related_concept

#### 2.4 龙虎榜数据表
- **dragon_tiger_list**: 龙虎榜主表
  - stock_code, trade_date, reason, close_price, change_percent, turnover_rate, net_amount, buy_amount, sell_amount
- **dragon_tiger_detail**: 龙虎榜明细表
  - stock_code, trade_date, rank_type, rank_no, trader_name, amount, trader_type, is_institution
- **realtime_dragon_tiger**: 实时龙虎榜表
  - stock_code, trade_date, update_time, net_inflow, large_buy_amount, large_sell_amount, main_force_ratio, rank_position

#### 2.5 资金流向数据表
- **northbound_fund_flow**: 北向资金流向表
  - stock_code, trade_date, buy_amount, sell_amount, net_amount, holding_amount, holding_ratio, rank_position
- **northbound_intraday**: 北向资金分时数据表
  - trade_date, trade_time, shanghai_net, shenzhen_net, total_net, shanghai_balance, shenzhen_balance
- **fund_flow_detail**: 资金流向明细表
  - stock_code, trade_date, time_stamp, large_buy, large_sell, medium_buy, medium_sell, small_buy, small_sell

#### 2.6 股票分类数据表
- **stock_classification**: 股票分类表
  - stock_code, classification_type, confidence_score, classification_date, features, update_reason
- **fund_type_features**: 资金类型特征表
  - stock_code, trade_date, institutional_ratio, retail_ratio, hot_money_ratio, volume_pattern, price_volatility

#### 2.7 市场情绪数据表
- **market_sentiment**: 市场情绪表
  - trade_date, update_time, limit_up_count, limit_down_count, advance_count, decline_count, sentiment_score
- **sector_strength**: 板块强度表
  - sector_code, trade_date, update_time, avg_change_percent, leading_stock, net_inflow, strength_score, rank_position

#### 2.8 用户数据表
- **users**: 用户信息表
  - user_id, username, email, password_hash, created_time, last_login
- **user_watchlist**: 用户自选股表
  - user_id, stock_code, add_time, sort_order
- **user_settings**: 用户设置表
  - user_id, setting_key, setting_value, updated_time

#### 2.9 新闻和公告表
- **news_articles**: 新闻文章表
  - article_id, title, content, source, publish_time, related_stocks, category
- **company_announcements**: 公司公告表
  - announcement_id, stock_code, title, content, announcement_type, publish_time
- **hot_topics**: 热点题材表
  - topic_id, topic_name, heat_score, related_stocks, start_date, end_date

#### 2.10 商品现货数据表
- **commodity_prices**: 商品价格表
  - commodity_code, commodity_name, price, change_percent, trade_date, category
- **commodity_stock_relation**: 商品股票关联表
  - commodity_code, stock_code, correlation_score, update_time

#### 2.11 龙头股和涨停数据表
- **leading_stocks**: 龙头股表
  - stock_code, sector_code, trade_date, leading_score, leading_type, duration_days
- **limit_up_genes**: 涨停基因表
  - stock_code, trade_date, gene_type, gene_score, success_rate, related_factors
- **stock_selection_pool**: 选股池表
  - pool_id, pool_name, stock_codes, selection_criteria, created_time, performance

#### 2.12 策略和回测数据表
- **trading_strategies**: 交易策略表
  - strategy_id, strategy_name, strategy_type, parameters, created_by, status
- **backtest_results**: 回测结果表
  - backtest_id, strategy_id, start_date, end_date, total_return, max_drawdown, sharpe_ratio
- **strategy_signals**: 策略信号表
  - signal_id, strategy_id, stock_code, signal_type, signal_time, price, confidence

### 3. 时序数据库设计 (InfluxDB)
- **realtime_quotes_ts**: 实时行情时序数据
  - time, stock_code, price, volume, amount, change_percent
- **minute_kline_ts**: 分钟K线时序数据
  - time, stock_code, open, high, low, close, volume, amount
- **fund_flow_ts**: 资金流向时序数据
  - time, stock_code, net_inflow, large_buy, large_sell, main_ratio

### 4. 缓存设计 (Redis)
- **realtime:quotes:{stock_code}**: 实时行情缓存 (TTL: 5秒)
- **ranking:dragon_tiger:net_inflow**: 龙虎榜排行缓存 (TTL: 60秒)
- **hot:concepts**: 热点概念缓存 (TTL: 300秒)
- **user:watchlist:{user_id}**: 用户自选股缓存 (TTL: 3600秒)
- **calc:stock_classification:{stock_code}**: 计算结果缓存 (TTL: 1800秒)

## 第五部分：技术架构设计

### 1. 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        前端展示层                              │
├─────────────────────────────────────────────────────────────┤
│  Web端(React/Vue)  │  移动端(React Native/Flutter)  │  小程序  │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                        API网关层                              │
├─────────────────────────────────────────────────────────────┤
│  Nginx/Kong  │  限流控制  │  身份认证  │  负载均衡  │  监控日志  │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                        微服务层                               │
├─────────────────────────────────────────────────────────────┤
│ 行情服务 │ 分析服务 │ 用户服务 │ 推送服务 │ 计算服务 │ 搜索服务 │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                        数据处理层                              │
├─────────────────────────────────────────────────────────────┤
│ 实时计算(Flink) │ 批处理(Spark) │ 消息队列(Kafka) │ 任务调度 │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                        数据存储层                              │
├─────────────────────────────────────────────────────────────┤
│ MySQL │ InfluxDB │ Redis │ Elasticsearch │ HDFS │ MinIO │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                        数据采集层                              │
├─────────────────────────────────────────────────────────────┤
│ 行情接口 │ 爬虫系统 │ 第三方API │ 文件导入 │ 实时推送 │
└─────────────────────────────────────────────────────────────┘
```

### 2. 数据采集层设计

#### 2.1 实时行情采集
**核心组件：**
- **MarketDataCollector**: 实时行情数据采集器
- **多数据源支持**: Tushare、新浪财经、腾讯财经、Level-2数据
- **断线重连机制**: 自动重连和数据校验
- **数据分发**: Kafka消息队列分发到下游系统

#### 2.2 数据源管理
**配置管理：**
- 数据源优先级配置
- 限流控制和错误重试
- 数据质量监控
- 自动切换备用数据源

### 3. 数据处理层设计

#### 3.1 实时计算引擎 (Apache Flink)
**主要任务：**
- 实时龙虎榜计算
- 市场情绪实时分析
- 集合竞价异动检测
- 资金流向实时统计

#### 3.2 批处理引擎 (Apache Spark)
**主要任务：**
- 股票分类模型训练
- 历史数据分析
- 特征工程处理
- 报表生成

#### 3.3 消息队列 (Apache Kafka)
**主要用途：**
- 实时数据流传输
- 系统解耦
- 数据缓冲
- 事件驱动架构

### 4. 微服务层设计

#### 4.1 行情服务 (Market Service)
**主要功能：**
- 实时行情数据API
- 历史行情查询
- K线数据服务
- 技术指标计算

#### 4.2 分析服务 (Analysis Service)
**主要功能：**
- 股票分类分析
- 市场情绪计算
- 龙虎榜分析
- 资金流向分析

#### 4.3 用户服务 (User Service)
**主要功能：**
- 用户认证授权
- 个人中心管理
- 自选股管理
- 个性化设置

#### 4.4 推送服务 (Notification Service)
**主要功能：**
- WebSocket实时推送
- 异动提醒
- 个性化通知
- 消息订阅管理

#### 4.5 搜索服务 (Search Service)
**主要功能：**
- 股票搜索
- 新闻搜索
- 全文检索
- 智能推荐

### 5. 部署架构设计

#### 5.1 容器化部署 (Docker + Kubernetes)
**主要组件：**
- Docker容器化应用
- Kubernetes集群管理
- 服务发现和负载均衡
- 自动扩缩容

#### 5.2 监控和运维
**监控体系：**
- Prometheus + Grafana监控
- ELK日志分析
- APM性能监控
- 告警通知系统

### 6. 技术选型建议

#### 6.1 开发语言和框架
**后端服务：**
- Python 3.9+ (FastAPI, Pandas, Scikit-learn)
- Java 11+ (Spring Boot, Apache Flink)
- Go 1.19+ (高性能服务)

**前端开发：**
- React 18+ (Web端)
- React Native 0.70+ (移动端)
- TypeScript (类型安全)

#### 6.2 数据库和存储
**关系型数据库：**
- MySQL 8.0 (主数据库)
- PostgreSQL 14+ (复杂查询)

**时序数据库：**
- InfluxDB 2.0 (时序数据)
- TimescaleDB (时序扩展)

**缓存和搜索：**
- Redis 7.0 (缓存)
- Elasticsearch 8.0 (搜索)

#### 6.3 部署和运维
**容器化：**
- Docker (容器化)
- Kubernetes (容器编排)

**监控运维：**
- Prometheus (监控)
- Grafana (可视化)
- ELK Stack (日志)

## 第六部分：实现路线图

### 1. 项目实施阶段规划

#### 阶段一：基础设施搭建 (4-6周)

**目标：** 建立基础的数据采集和存储能力

**主要任务：**
1. **环境搭建**
   - 搭建开发、测试、生产环境
   - 配置Docker容器化环境
   - 建立CI/CD流水线

2. **数据库设计与实现**
   - 创建MySQL主数据库
   - 配置InfluxDB时序数据库
   - 部署Redis缓存集群
   - 实现数据库分库分表策略

3. **基础数据采集**
   - 实现Tushare数据接口对接
   - 开发新浪财经爬虫
   - 建立基础行情数据采集
   - 实现数据清洗和校验

4. **核心服务框架**
   - 搭建FastAPI微服务框架
   - 实现API网关和负载均衡
   - 建立服务注册与发现
   - 配置监控和日志系统

**交付物：**
- 完整的基础设施环境
- 基础数据采集系统
- 核心数据库表结构
- 基础API服务框架

#### 阶段二：核心功能开发 (8-10周)

**目标：** 实现开盘啦的六大核心功能

**2.1 实时行情与龙虎榜 (3周)**
- 实时行情数据采集和展示
- 实时龙虎榜计算算法
- 大单资金流向监控
- WebSocket实时推送

**2.2 集合竞价异动 (2周)**
- 集合竞价数据采集
- 异动检测算法实现
- 题材关联分析
- 异动预警系统

**2.3 股票分类系统 (3周)**
- 机器学习分类模型训练
- 特征工程实现
- 分类算法优化
- 分类结果展示

**2.4 市场情绪分析 (2周)**
- 市场情绪指标计算
- 板块强度分析
- 情绪可视化展示
- 历史情绪对比

**交付物：**
- 六大核心功能API
- 实时数据处理系统
- 机器学习分类模型
- 基础前端展示页面

#### 阶段三：高级功能与优化 (6-8周)

**目标：** 完善高级功能和性能优化

**3.1 北向资金分析 (2周)**
- 北向资金数据采集
- 分时资金流向分析
- 持仓变化追踪
- 资金流向可视化

**3.2 深度龙虎榜分析 (3周)**
- 游资席位识别
- 主力操作模式分析
- 协同操作检测
- 个性化组合管理

**3.3 商品现货联动 (2周)**
- 商品价格数据采集
- 商品股票关联分析
- 行业轮动预测
- 联动效应展示

**3.4 性能优化 (1周)**
- 数据库查询优化
- 缓存策略优化
- API响应时间优化
- 系统负载测试

**交付物：**
- 完整的高级分析功能
- 优化的系统性能
- 完善的用户界面
- 详细的技术文档

#### 阶段四：用户体验与扩展 (4-6周)

**目标：** 提升用户体验和系统扩展性

**4.1 用户系统 (2周)**
- 用户注册登录
- 个人中心功能
- 自选股管理
- 个性化设置

**4.2 移动端开发 (3周)**
- React Native移动应用
- 核心功能移动适配
- 推送通知系统
- 离线数据缓存

**4.3 高级分析工具 (1周)**
- 自定义指标计算
- 策略回测功能
- 数据导出功能
- API开放平台

**交付物：**
- 完整的用户系统
- 移动端应用
- 高级分析工具
- 开放API文档

### 2. 团队配置建议

#### 2.1 核心团队 (8-10人)
- **技术负责人 × 1**: 整体架构设计、技术选型决策
- **后端开发 × 3**: 微服务开发、数据处理系统、API接口实现
- **前端开发 × 2**: Web端开发、移动端开发、用户界面设计
- **数据工程师 × 2**: 数据采集系统、ETL流程设计、数据质量保证
- **算法工程师 × 1**: 机器学习模型、量化分析算法、数据挖掘
- **运维工程师 × 1**: 系统部署运维、监控告警、性能优化

#### 2.2 扩展团队 (可选)
- **产品经理 × 1**: 需求分析、产品规划、用户体验
- **UI/UX设计师 × 1**: 界面设计、交互设计、视觉规范
- **测试工程师 × 1**: 功能测试、性能测试、自动化测试

### 3. 预算估算

#### 3.1 人力成本 (按月)
- 核心团队 (10人)：约226K/月
- 项目周期 (6个月)：约1,356K

#### 3.2 基础设施成本 (按月)
- 云服务器：约6K/月
- 数据服务：约15K/月
- CDN和其他：约1K/月
- 项目周期 (6个月)：约132K

#### 3.3 软件许可成本
- 开发工具和数据库许可：约45K/年

#### 3.4 总预算估算
- **总计：约163万** (6个月项目周期)

### 4. 风险评估与应对

#### 4.1 技术风险
- **数据源稳定性问题**: 多数据源备份，自动切换机制
- **高并发性能瓶颈**: 分布式架构，缓存优化，负载均衡
- **数据准确性问题**: 多重数据校验，异常检测，人工审核

#### 4.2 业务风险
- **监管政策变化**: 关注政策动态，预留合规调整空间
- **市场竞争激烈**: 差异化功能，用户体验优化
- **数据成本上升**: 自建数据采集，降低依赖度

### 5. 成功指标

#### 5.1 技术指标
- API响应时间 < 100ms
- 系统可用性 > 99.9%
- 数据准确率 > 99.5%
- 并发用户数 > 10,000

#### 5.2 业务指标
- 用户注册数 > 50,000
- 日活跃用户 > 5,000
- 用户留存率 > 60%
- 功能使用率 > 80%

## 总结

本文档详细分析了"开盘啦"项目的功能模块、数据结构、计算方法、数据源需求、数据库设计、技术架构和实现路线图。通过系统性的分析，为实现类似的股票数据分析平台提供了完整的技术蓝图和实施指导。

### 核心价值
1. **完整的技术实现方案**: 从数据采集到前端展示的全链路设计
2. **详细的开发指导**: 具体的算法实现和代码架构
3. **实用的项目管理**: 阶段规划、团队配置、预算估算
4. **可操作的实施路径**: 分阶段实现，降低项目风险

### 下一步建议
1. **技术验证**: 先实现核心功能的MVP版本
2. **数据测试**: 验证数据源的稳定性和准确性
3. **团队组建**: 按照建议配置核心开发团队
4. **原型开发**: 快速开发功能原型进行验证

这份文档可以作为实现自己的股票数据分析平台的完整技术蓝图和实施指南。
